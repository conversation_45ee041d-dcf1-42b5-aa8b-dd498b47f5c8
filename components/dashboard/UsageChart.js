"use client";

import { useState } from "react";

const UsageChart = ({ chartData = { "24h": [], "7d": [], "30d": [] } }) => {
  const [timeRange, setTimeRange] = useState("7d");
  
  const currentData = chartData[timeRange] || [];
  const maxValue = currentData.length > 0 ? Math.max(...currentData.map(d => Math.max(d.calls, d.credits))) : 0;
  const allZero = currentData.every(d => d.calls === 0 && d.credits === 0);

  return (
    <div className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-xl border border-gray-200 dark:border-gray-700 p-6 shadow-lg">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            API Usage Overview
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            Track your API calls and credit consumption
          </p>
        </div>
        
        {/* Time Range Selector */}
        <div className="flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
          {["24h", "7d", "30d"].map((range) => (
            <button
              key={range}
              onClick={() => setTimeRange(range)}
              className={`px-3 py-1 text-sm font-medium rounded-md transition-colors ${
                timeRange === range
                  ? "bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm"
                  : "text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"
              }`}
            >
              {range}
            </button>
          ))}
        </div>
      </div>

      {/* Chart Area */}
      <div className="relative h-64 overflow-x-auto">
        {allZero ? (
          <div className="flex items-center justify-center h-full text-gray-400 dark:text-gray-500 text-sm">
            No usage data for this period.
          </div>
        ) : (
          <div className="absolute inset-0 flex items-end space-x-2 min-w-max pr-4">
            {currentData.map((data, index) => (
              <div key={index} className="flex flex-col items-center space-y-2" style={{ minWidth: '32px' }}>
                {/* Bars */}
                <div className="w-full flex space-x-1 items-end h-48">
                  {/* API Calls Bar */}
                  <div className="flex-1 bg-gradient-to-t from-purple-500 to-purple-400 rounded-t-sm relative group" style={{ minWidth: '10px' }}>
                    <div 
                      className="w-full bg-gradient-to-t from-purple-500 to-purple-400 rounded-t-sm transition-all duration-300"
                      style={{ height: maxValue > 0 ? `${(data.calls / maxValue) * 100}%` : '0%' }}
                    ></div>
                    {/* Tooltip */}
                    <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 dark:bg-gray-700 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                      {data.calls} calls
                    </div>
                  </div>
                  {/* Credits Bar */}
                  <div className="flex-1 bg-gradient-to-t from-blue-500 to-blue-400 rounded-t-sm relative group" style={{ minWidth: '10px' }}>
                    <div 
                      className="w-full bg-gradient-to-t from-blue-500 to-blue-400 rounded-t-sm transition-all duration-300"
                      style={{ height: maxValue > 0 ? `${(data.credits / maxValue) * 100}%` : '0%' }}
                    ></div>
                    {/* Tooltip */}
                    <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 dark:bg-gray-700 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                      {data.credits} credits
                    </div>
                  </div>
                </div>
                {/* Time Label */}
                <span className="text-xs text-gray-600 dark:text-gray-400 font-medium whitespace-nowrap">
                  {data.time}
                </span>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Legend */}
      <div className="flex items-center justify-center space-x-6 mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-gradient-to-r from-purple-500 to-purple-400 rounded-sm"></div>
          <span className="text-sm text-gray-600 dark:text-gray-400">API Calls</span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-gradient-to-r from-blue-500 to-blue-400 rounded-sm"></div>
          <span className="text-sm text-gray-600 dark:text-gray-400">Credits Used</span>
        </div>
      </div>
    </div>
  );
};

export default UsageChart;

"use client";

import { useState } from "react";

const UsageChart = ({ chartData = { "24h": [], "7d": [], "30d": [] } }) => {
  const [timeRange, setTimeRange] = useState("7d");

  // Ensure chartData is properly structured
  const safeChartData = {
    "24h": chartData["24h"] || [],
    "7d": chartData["7d"] || [],
    "30d": chartData["30d"] || []
  };

  const currentData = safeChartData[timeRange] || [];
  const maxValue = currentData.length > 0 ? Math.max(...currentData.map(d => Math.max(Number(d.calls) || 0, Number(d.credits) || 0))) : 0;
  const allZero = currentData.length === 0 || currentData.every(d => (Number(d.calls) || 0) === 0 && (Number(d.credits) || 0) === 0);

  // Debug logging
  console.log("UsageChart Debug:", {
    timeRange,
    currentData,
    maxValue,
    allZero,
    chartData: safeChartData
  });

  // Add test data button for debugging (remove in production)
  const addTestData = () => {
    console.log("Adding test data for debugging...");
    const testData = {
      "24h": Array.from({ length: 24 }, (_, i) => ({
        time: `${i.toString().padStart(2, '0')}:00`,
        calls: Math.floor(Math.random() * 50),
        credits: Math.floor(Math.random() * 100)
      })),
      "7d": ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].map(day => ({
        time: day,
        calls: Math.floor(Math.random() * 200),
        credits: Math.floor(Math.random() * 400)
      })),
      "30d": Array.from({ length: 4 }, (_, i) => ({
        time: `Week ${i + 1}`,
        calls: Math.floor(Math.random() * 1000),
        credits: Math.floor(Math.random() * 2000)
      }))
    };
    console.log("Test data:", testData);
  };

  return (
    <div className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-xl border border-gray-200 dark:border-gray-700 p-6 shadow-lg">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            API Usage Overview
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            Track your API calls and credit consumption
          </p>
        </div>
        
        {/* Time Range Selector */}
        <div className="flex items-center space-x-2">
          {/* Debug button - remove in production */}
          <button
            onClick={addTestData}
            className="px-2 py-1 text-xs bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
            title="Debug: Log test data"
          >
            Debug
          </button>

          <div className="flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
            {["24h", "7d", "30d"].map((range) => (
              <button
                key={range}
                onClick={() => setTimeRange(range)}
                className={`px-3 py-1 text-sm font-medium rounded-md transition-colors ${
                  timeRange === range
                    ? "bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm"
                    : "text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"
                }`}
              >
                {range}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Chart Area */}
      <div className="relative h-64 overflow-x-auto">
        {allZero || currentData.length === 0 ? (
          <div className="flex items-center justify-center h-full text-gray-400 dark:text-gray-500 text-sm">
            No usage data for this period.
          </div>
        ) : (
          <div className="flex items-end justify-center space-x-2 h-full px-4">
            {currentData.map((data, index) => {
              const calls = Number(data.calls) || 0;
              const credits = Number(data.credits) || 0;
              const callsHeight = maxValue > 0 ? Math.max((calls / maxValue) * 180, calls > 0 ? 4 : 2) : 2;
              const creditsHeight = maxValue > 0 ? Math.max((credits / maxValue) * 180, credits > 0 ? 4 : 2) : 2;

              return (
                <div key={index} className="flex flex-col items-center space-y-2 flex-1 max-w-16">
                  {/* Bars Container */}
                  <div className="flex space-x-1 items-end h-48 w-full justify-center">
                    {/* API Calls Bar */}
                    <div className="relative group flex-1 max-w-6">
                      <div
                        className="w-full bg-gradient-to-t from-purple-500 to-purple-400 rounded-t-sm transition-all duration-300 min-h-[2px]"
                        style={{ height: `${callsHeight}px` }}
                      ></div>
                      {/* Tooltip */}
                      <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 dark:bg-gray-700 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-10">
                        {calls} calls
                      </div>
                    </div>
                    {/* Credits Bar */}
                    <div className="relative group flex-1 max-w-6">
                      <div
                        className="w-full bg-gradient-to-t from-blue-500 to-blue-400 rounded-t-sm transition-all duration-300 min-h-[2px]"
                        style={{ height: `${creditsHeight}px` }}
                      ></div>
                      {/* Tooltip */}
                      <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 dark:bg-gray-700 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-10">
                        {credits} credits
                      </div>
                    </div>
                  </div>
                  {/* Time Label */}
                  <span className="text-xs text-gray-600 dark:text-gray-400 font-medium whitespace-nowrap">
                    {data.time}
                  </span>
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* Legend */}
      <div className="flex items-center justify-center space-x-6 mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-gradient-to-r from-purple-500 to-purple-400 rounded-sm"></div>
          <span className="text-sm text-gray-600 dark:text-gray-400">API Calls</span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-gradient-to-r from-blue-500 to-blue-400 rounded-sm"></div>
          <span className="text-sm text-gray-600 dark:text-gray-400">Credits Used</span>
        </div>
      </div>
    </div>
  );
};

export default UsageChart;

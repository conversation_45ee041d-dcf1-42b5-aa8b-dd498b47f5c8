"use client";

import { useState } from "react";

const ApiKeyManager = ({ apiKey: propApiKey, apiKeyCreatedAt, apiKeyLastUsed, apiKeyUsageCount }) => {
  const [showKey, setShowKey] = useState(false);
  const [isRegenerating, setIsRegenerating] = useState(false);
  const [copySuccess, setCopySuccess] = useState(false);

  // Use provided API key or fallback to mock data
  const apiKey = propApiKey || "api-key-1234567890abcdef1234567890abcdef";

  // Create masked version of the API key
  const createMaskedKey = (key) => {
    if (!key || key.length < 8) return key;
    const prefix = key.substring(0, 8);
    const suffix = key.substring(key.length - 4);
    const maskedMiddle = "•".repeat(Math.max(0, key.length - 12));
    return `${prefix}${maskedMiddle}${suffix}`;
  };

  const maskedKey = createMaskedKey(apiKey);

  const handleCopyKey = async () => {
    try {
      await navigator.clipboard.writeText(apiKey);
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    } catch (err) {
      console.error('Failed to copy API key:', err);
    }
  };

  const handleRegenerateKey = async () => {
    setIsRegenerating(true);
    // Simulate API call
    setTimeout(() => {
      setIsRegenerating(false);
      // You could add a toast notification here
    }, 2000);
  };

  return (
    <div className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-xl border border-gray-200 dark:border-gray-700 p-6 shadow-lg">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          API Key Management
        </h3>
        <div className="p-2 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg text-white">
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-3a1 1 0 011-1h2.586l6.243-6.243A6 6 0 0121 9z" />
          </svg>
        </div>
      </div>

      <div className="space-y-4">
        {/* API Key Display */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Your API Key
          </label>
          <div className="flex items-center space-x-2">
            <div className="flex-1 min-w-0 px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg font-mono text-sm overflow-hidden">
              <div className="truncate">
                {showKey ? apiKey : maskedKey}
              </div>
            </div>
            <button
              onClick={() => setShowKey(!showKey)}
              className="flex-shrink-0 p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
              title={showKey ? "Hide key" : "Show key"}
            >
              {showKey ? (
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                </svg>
              ) : (
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
              )}
            </button>
          </div>
        </div>

        {/* API Key Stats */}
        {(apiKeyCreatedAt || apiKeyLastUsed || apiKeyUsageCount !== undefined) && (
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
            {apiKeyCreatedAt && (
              <div className="text-center">
                <div className="text-xs text-gray-500 dark:text-gray-400">Created</div>
                <div className="text-sm font-medium text-gray-900 dark:text-white">
                  {new Date(apiKeyCreatedAt).toLocaleDateString()}
                </div>
              </div>
            )}
            {apiKeyLastUsed && (
              <div className="text-center">
                <div className="text-xs text-gray-500 dark:text-gray-400">Last Used</div>
                <div className="text-sm font-medium text-gray-900 dark:text-white">
                  {new Date(apiKeyLastUsed).toLocaleDateString()}
                </div>
              </div>
            )}
            {apiKeyUsageCount !== undefined && (
              <div className="text-center">
                <div className="text-xs text-gray-500 dark:text-gray-400">Total Uses</div>
                <div className="text-sm font-medium text-gray-900 dark:text-white">
                  {apiKeyUsageCount.toLocaleString()}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-3">
          <button
            onClick={handleCopyKey}
            className={`flex-1 flex items-center justify-center px-4 py-2 rounded-lg transition-colors ${
              copySuccess
                ? 'bg-green-100 hover:bg-green-200 dark:bg-green-900/30 dark:hover:bg-green-900/50 text-green-700 dark:text-green-300'
                : 'bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300'
            }`}
          >
            {copySuccess ? (
              <>
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                Copied!
              </>
            ) : (
              <>
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                </svg>
                Copy Key
              </>
            )}
          </button>
          
          <button
            onClick={handleRegenerateKey}
            disabled={isRegenerating}
            className="flex-1 flex items-center justify-center px-4 py-2 bg-red-100 hover:bg-red-200 dark:bg-red-900/30 dark:hover:bg-red-900/50 text-red-700 dark:text-red-300 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isRegenerating ? (
              <>
                <svg className="w-4 h-4 mr-2 animate-spin" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Regenerating...
              </>
            ) : (
              <>
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                Regenerate
              </>
            )}
          </button>
        </div>

        {/* Warning Message */}
        <div className="p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
          <div className="flex">
            <svg className="w-5 h-5 text-yellow-400 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <div className="text-sm text-yellow-800 dark:text-yellow-200">
              <p className="font-medium">Keep your API key secure</p>
              <p className="mt-1">Don&apos;t share your API key in publicly accessible areas such as GitHub, client-side code, and so forth.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ApiKeyManager;

"use client";

const WebSocketStatus = ({
  websocketSessions = [],
  maxWebSocketConnections = 10
}) => {

  // Transform real WebSocket session data into display format
  const connections = websocketSessions.map(session => {
    // If disconnected_at is null, the session is still connected
    // We'll consider it "connected" if not disconnected, regardless of last_activity time
    // In a real-time system, last_activity might not be updated frequently
    const isDisconnected = session.disconnected_at !== null;

    // Calculate time since last activity for display purposes
    const lastActivity = new Date(session.last_activity);
    const now = new Date();
    const minutesSinceActivity = (now - lastActivity) / (1000 * 60);

    // Determine status: if not disconnected and has recent activity (within 30 minutes), show as connected
    // Otherwise show as idle but still count as active connection
    let status;
    if (isDisconnected) {
      status = "disconnected";
    } else if (minutesSinceActivity < 30) {
      status = "connected";
    } else {
      status = "idle";
    }

    return {
      id: session.id,
      sessionId: session.session_id,
      connectionId: session.connection_id,
      streams: session.subscribed_streams || [],
      status: status,
      connectedAt: session.connected_at,
      lastActivity: session.last_activity,
      ipAddress: session.ip_address,
      userAgent: session.user_agent,
      isActiveConnection: !isDisconnected // This determines if it counts toward active connections
    };
  });

  const activeConnections = connections.filter(conn => conn.isActiveConnection);

  // Debug logging
  console.log("!!!WEBSOCKET DEBUG", {
    rawSessions: websocketSessions,
    processedConnections: connections,
    activeConnections: activeConnections,
    activeCount: activeConnections.length
  });

  const getStatusColor = (status) => {
    switch (status) {
      case "connected":
        return "text-green-600 dark:text-green-400";
      case "idle":
        return "text-yellow-600 dark:text-yellow-400";
      case "disconnected":
        return "text-red-600 dark:text-red-400";
      default:
        return "text-gray-600 dark:text-gray-400";
    }
  };

  const getStatusBg = (status) => {
    switch (status) {
      case "connected":
        return "bg-green-100 dark:bg-green-900/30 border-green-200 dark:border-green-800";
      case "idle":
        return "bg-yellow-100 dark:bg-yellow-900/30 border-yellow-200 dark:border-yellow-800";
      case "disconnected":
        return "bg-red-100 dark:bg-red-900/30 border-red-200 dark:border-red-800";
      default:
        return "bg-gray-100 dark:bg-gray-700 border-gray-200 dark:border-gray-600";
    }
  };

  const formatTime = (timestamp) => {
    const date = new Date(timestamp);
    // Use consistent locale to prevent hydration mismatch
    return date.toLocaleTimeString("en-US", { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <div className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-xl border border-gray-200 dark:border-gray-700 p-6 shadow-lg">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            WebSocket Status
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            {activeConnections.length}/{maxWebSocketConnections} connections active
          </p>
        </div>
        <div className="p-2 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg text-white">
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0" />
          </svg>
        </div>
      </div>

      {/* Overall Status */}
      <div className={`p-4 rounded-lg border mb-6 ${
        activeConnections.length > 0
          ? "bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800"
          : "bg-gray-50 dark:bg-gray-900/20 border-gray-200 dark:border-gray-800"
      }`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className={`w-3 h-3 rounded-full ${
              activeConnections.length > 0 ? "bg-green-400 animate-pulse" : "bg-gray-400"
            }`}></div>
            <div>
              <p className={`font-medium ${
                activeConnections.length > 0
                  ? "text-green-800 dark:text-green-200"
                  : "text-gray-800 dark:text-gray-200"
              }`}>
                {activeConnections.length > 0 ? "Connected" : "No Active Connections"}
              </p>
              <p className={`text-sm ${
                activeConnections.length > 0
                  ? "text-green-600 dark:text-green-400"
                  : "text-gray-600 dark:text-gray-400"
              }`}>
                {activeConnections.length} active connection{activeConnections.length !== 1 ? 's' : ''}
              </p>
            </div>
          </div>
          <div className="text-right">
            <p className="text-sm text-gray-600 dark:text-gray-400">Limit</p>
            <p className="font-medium text-gray-900 dark:text-white">
              {activeConnections.length}/{maxWebSocketConnections}
            </p>
          </div>
        </div>
      </div>

      {/* Connection List */}
      <div className="space-y-3">
        <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
          {connections.length > 0 ? "Active Sessions" : "No Active Sessions"}
        </h4>

        {connections.length > 0 ? (
          connections.map((connection) => (
            <div key={connection.id} className={`p-3 rounded-lg border ${getStatusBg(connection.status)}`}>
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-2">
                  <span className="font-medium text-gray-900 dark:text-white text-sm">
                    Session {connection.sessionId.slice(0, 8)}...
                  </span>
                  <span className={`text-xs font-medium ${getStatusColor(connection.status)}`}>
                    {connection.status}
                  </span>
                </div>
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {formatTime(connection.connectedAt)}
                </span>
              </div>

              <div className="space-y-1">
                <div className="flex justify-between text-xs text-gray-600 dark:text-gray-400">
                  <span>Streams: {connection.streams.length > 0 ? connection.streams.join(', ') : 'None'}</span>
                  {connection.isActiveConnection && (
                    <span className={`${
                      connection.status === "connected"
                        ? "text-green-600 dark:text-green-400"
                        : "text-yellow-600 dark:text-yellow-400"
                    }`}>
                      ● {connection.status === "connected" ? "Live" : "Idle"}
                    </span>
                  )}
                </div>
                <div className="flex justify-between text-xs text-gray-600 dark:text-gray-400">
                  <span>IP: {connection.ipAddress}</span>
                  <span>Last Activity: {formatTime(connection.lastActivity)}</span>
                </div>
              </div>
            </div>
          ))
        ) : (
          <div className="p-4 text-center text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-800/50 rounded-lg border border-dashed border-gray-300 dark:border-gray-600">
            <svg className="w-8 h-8 mx-auto mb-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0" />
            </svg>
            <p className="text-sm">No WebSocket connections active</p>
            <p className="text-xs mt-1">Connect to start receiving real-time data</p>
          </div>
        )}
      </div>

      {/* Quick Actions */}
      <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
        <button className="w-full flex items-center justify-center px-4 py-2 bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600 text-white rounded-lg transition-all duration-200 text-sm font-medium">
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          New Connection
        </button>
      </div>
    </div>
  );
};

export default WebSocketStatus;

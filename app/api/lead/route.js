import { NextResponse } from "next/server";
import { prisma } from "@/libs/prisma";
import { cache, CacheKeys, checkRateLimit } from "@/libs/redis";
import { z } from "zod";

const RATE_LIMIT_REQUESTS = 5;
const RATE_LIMIT_WINDOW_SECONDS = 60;
const LEAD_CACHE_TTL_SECONDS = 86400; // 24 hours

const LeadSchema = z.object({
  email: z.string().email().trim().toLowerCase(),
});

// This route is used to store the leads that are generated from the landing page.
// The API call is initiated by <ButtonLead /> component
// Duplicate emails just return 200 OK
export async function POST(req) {
  try {
    const body = await req.json();
    const parseResult = LeadSchema.safeParse(body);
    if (!parseResult.success) {
      return NextResponse.json({ error: "Invalid email format" }, { status: 400 });
    }
    const { email } = parseResult.data;

    // Rate limiting - 5 requests per minute per IP
    const clientIP = req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || 'unknown';
    const rateLimitKey = CacheKeys.rateLimit(clientIP, 'lead');
    const rateLimit = await checkRateLimit(rateLimitKey, RATE_LIMIT_REQUESTS, RATE_LIMIT_WINDOW_SECONDS);

    if (!rateLimit.allowed) {
      return NextResponse.json(
        { error: "Too many requests. Please try again later." },
        { status: 429 }
      );
    }

    // Check cache first
    const cacheKey = CacheKeys.lead(email);
    const cachedLead = await cache.get(cacheKey);

    if (cachedLead) {
      // Lead already exists in cache, return success without database hit
      return NextResponse.json({ message: "Lead processed successfully" });
    }

    // Check if lead already exists in database
    const existingLead = await prisma.lead.findUnique({
      where: { email }
    });

    if (!existingLead) {
      // Create new lead
      const newLead = await prisma.lead.create({
        data: { email }
      });

      // Cache the lead for 24 hours
      await cache.set(cacheKey, { id: newLead.id, email: newLead.email }, LEAD_CACHE_TTL_SECONDS);

      console.log(`✅ New lead created:`, { id: newLead.id });
    } else {
      // Cache existing lead for 24 hours
      await cache.set(cacheKey, { id: existingLead.id, email: existingLead.email }, LEAD_CACHE_TTL_SECONDS);
    }

    // Here you can add your own logic
    // For instance, sending a welcome email (use the sendEmail helper function from /libs/resend)
    // For instance, adding to email marketing list

    return NextResponse.json({ message: "Lead processed successfully" });
  } catch (e) {
    console.error("Lead API error:", e);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

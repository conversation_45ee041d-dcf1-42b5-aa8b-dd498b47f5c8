import { NextResponse } from 'next/server';

// Mock admin API key store (replace with DB or secure store in production)
const adminApiKeys = [
  {
    key: process.env.ADMIN_API_KEY || 'demo_admin_api_key_12345',
    allowedIps: ['127.0.0.1', '::1'], // Add production IPs here
    expiresAt: null, // Set to a Date string for expiration, or null for no expiry
  },
];

export function middleware(req) {
  const apiKey = req.headers.get('x-admin-api-key');
  const clientIp = req.headers.get('x-forwarded-for') || req.ip || req.connection?.remoteAddress;
  const now = new Date();

  const keyRecord = adminApiKeys.find(k => k.key === apiKey);

  if (!keyRecord) {
    return new NextResponse(JSON.stringify({ error: 'Invalid or missing admin API key.' }), { status: 401 });
  }

  // Optional: IP allowlisting
  if (keyRecord.allowedIps && keyRecord.allowedIps.length > 0 && !keyRecord.allowedIps.includes(clientIp)) {
    return new NextResponse(JSON.stringify({ error: 'IP address not allowed for this admin API key.' }), { status: 403 });
  }

  // Optional: Key expiration
  if (keyRecord.expiresAt && now > new Date(keyRecord.expiresAt)) {
    return new NextResponse(JSON.stringify({ error: 'Admin API key expired.' }), { status: 401 });
  }

  // Allow request to proceed
  return NextResponse.next();
}

export const config = {
  matcher: ['/api/admin/:path*'],
}; 
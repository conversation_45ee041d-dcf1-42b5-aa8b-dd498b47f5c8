import { query } from "@/libs/postgres";
import { query as queryApi } from "@/libs/postgres_api";
import { getServerSession } from "next-auth";
import { authOptions } from "@/libs/next-auth";

import DashboardHeader from "@/components/dashboard/DashboardHeader";
import StatsOverview from "@/components/dashboard/StatsOverview";
import ApiKeyManager from "@/components/dashboard/ApiKeyManager";
import UsageChart from "@/components/dashboard/UsageChart";
import WebSocketStatus from "@/components/dashboard/WebSocketStatus";
import QuickActions from "@/components/dashboard/QuickActions";
import RecentActivity from "@/components/dashboard/RecentActivity";

export const dynamic = "force-dynamic";

// This is a private page: It's protected by the layout.js component which ensures the user is authenticated.
// It's a server compoment which means you can fetch data (like the user profile) before the page is rendered.
// See https://shipfa.st/docs/tutorials/private-page
export default async function Dashboard() {
  const session = await getServerSession(authOptions);
  console.log("!!!SESSION", session);

  // ------------------------------------------------------------ //
  // get the apiKey from the database
  const apiKey = await query('SELECT "apiKey" FROM users WHERE id = $1', [
    session.user.id,
  ]);
  console.log("!!!API KEY", apiKey.rows[0].apiKey);

  // ------------------------------------------------------------ //
  // get the api data
  const apiData = await queryApi(
    "SELECT a.*, b.name, b.max_credits_per_month, b.allowed_endpoints, b.max_websocket_connections FROM users a LEFT JOIN access_tiers b ON a.tier_id = b.id WHERE a.id = $1",
    [apiKey.rows[0].apiKey]
  );
  /* 
  {
    id: 'b5180f95-c6a0-40fd-83d8-e795b8388e40',
    email: '<EMAIL>',
    password_hash: '$2a$10$rOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZQZQZOzJqQZQZQZQZQ',
    api_key: 'test_basic_api_key_12345',
    tier_id: 2,
    credits_remaining: 999932,
    credits_used_this_month: 71,
    total_credits_purchased: 0,
    is_active: true,
    last_login: null,
    created_at: 2025-06-04T05:13:34.252Z,
    updated_at: 2025-06-08T03:22:23.848Z,
    name: 'basic',
    max_credits_per_month: 1000000,
    allowed_endpoints: [
      '/api/v1/status',
      '/api/v1/kol-feed/history',
      '/api/v1/smart-money/daily-trends/most-bought-tokens',
      '/api/v1/kol-feed/history',
      '/api/v1/smart-money/daily-trends/most-sol-tokens',
      '/api/v1/smart-money/daily-trends/daily-flows-sol',
      '/api/v1/smart-money/daily-trends/daily-flows-meme',
      '/api/v1/smart-money/top-tokens/24h',
      '/api/v1/smart-money/top-tokens/3d',
      '/api/v1/smart-money/top-tokens/7d',
      '/api/v1/smart-money/bottom-tokens/24h',
      '/api/v1/smart-money/bottom-tokens/3d',
      '/api/v1/smart-money/bottom-tokens/7d'
    ],
    max_websocket_connections: 3
  }
  */
  console.log("!!!API DATA", apiData.rows);

  // ------------------------------------------------------------ //
  // get usage data
  const apiUsageData = await queryApi(
    `SELECT 
        COUNT(*) as total_requests,
        SUM(credits_consumed) as total_credits_consumed,
        AVG(response_time_ms) as avg_response_time,
        COUNT(DISTINCT endpoint) as unique_endpoints,
        DATE_TRUNC('day', created_at) as date,
        COUNT(*) as daily_requests
     FROM api_usage_logs 
     WHERE user_id = $1 AND created_at >= CURRENT_DATE - INTERVAL '1 day'
     GROUP BY DATE_TRUNC('day', created_at)
     ORDER BY date DESC`,
    [apiKey.rows[0].apiKey]
  );
  /* 
  {
    total_requests: '2',
    total_credits_consumed: '4',
    avg_response_time: null,
    unique_endpoints: '1',
    date: 2025-06-08T00:00:00.000Z,
    daily_requests: '2'
  }
  */
  const usageData = apiUsageData.rows.length > 0 ? apiUsageData.rows[0] : {
    total_requests: 0,
    total_credits_consumed: 0,
    avg_response_time: null,
    unique_endpoints: 0,
    date: new Date(),
    daily_requests: 0
  };
  console.log("!!!API USAGE DATA", apiUsageData.rows);

  // ------------------------------------------------------------ //
  // get websocket sessions websocket_sessions
  const websocketSessions = await queryApi(
    "SELECT * FROM websocket_sessions WHERE user_id = $1 AND disconnected_at IS NULL",
    [apiKey.rows[0].apiKey]
  );
  console.log("!!!WEBSOCKET SESSIONS", websocketSessions.rows);

  // ------------------------------------------------------------ //
  // get usage chart data
  // 24h: group by hour for the last 24 hours
  const usage24h = await queryApi(
    `SELECT 
      TO_CHAR(created_at, 'HH24:00') as time,
      COUNT(*) as calls,
      COALESCE(SUM(credits_consumed), 0) as credits
    FROM api_usage_logs
    WHERE user_id = $1 AND created_at >= NOW() - INTERVAL '24 hours'
    GROUP BY time
    ORDER BY time ASC`,
    [apiKey.rows[0].apiKey]
  );

  // 7d: group by day for the last 7 days
  const usage7d = await queryApi(
    `SELECT 
      TO_CHAR(created_at, 'Dy') as time,
      COUNT(*) as calls,
      COALESCE(SUM(credits_consumed), 0) as credits
    FROM api_usage_logs
    WHERE user_id = $1 AND created_at >= NOW() - INTERVAL '7 days'
    GROUP BY time, DATE_TRUNC('day', created_at)
    ORDER BY DATE_TRUNC('day', created_at) ASC`,
    [apiKey.rows[0].apiKey]
  );

  // 30d: group by week for the last 30 days
  const usage30d = await queryApi(
    `SELECT 
      CONCAT('Week ', EXTRACT(WEEK FROM created_at)) as time,
      COUNT(*) as calls,
      COALESCE(SUM(credits_consumed), 0) as credits
    FROM api_usage_logs
    WHERE user_id = $1 AND created_at >= NOW() - INTERVAL '30 days'
    GROUP BY time, EXTRACT(WEEK FROM created_at)
    ORDER BY EXTRACT(WEEK FROM created_at) ASC`,
    [apiKey.rows[0].apiKey]
  );

  // Pad 24h data (00:00 to 23:00)
  const hours = Array.from({ length: 24 }, (_, i) => `${i.toString().padStart(2, '0')}:00`);
  const usage24hMap = Object.fromEntries(usage24h.rows.map(row => [row.time, row]));
  const padded24h = hours.map(time => ({
    time,
    calls: Number(usage24hMap[time]?.calls || 0),
    credits: Number(usage24hMap[time]?.credits || 0)
  }));

  // Pad 7d data (Mon-Sun)
  const days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
  const usage7dMap = Object.fromEntries(usage7d.rows.map(row => [row.time, row]));
  const padded7d = days.map(time => ({
    time,
    calls: Number(usage7dMap[time]?.calls || 0),
    credits: Number(usage7dMap[time]?.credits || 0)
  }));

  // Pad 30d data (last 4 weeks, e.g., Week 21, Week 22, Week 23, Week 24)
  const now = new Date();
  const currentWeek = Number(
    (function getWeekNumber(d) {
      d = new Date(Date.UTC(d.getFullYear(), d.getMonth(), d.getDate()));
      d.setUTCDate(d.getUTCDate() + 4 - (d.getUTCDay()||7));
      const yearStart = new Date(Date.UTC(d.getUTCFullYear(),0,1));
      const weekNo = Math.ceil((((d - yearStart) / 86400000) + 1)/7);
      return weekNo;
    })(now)
  );
  const weeks = Array.from({ length: 4 }, (_, i) => `Week ${currentWeek - 3 + i}`);
  const usage30dMap = Object.fromEntries(usage30d.rows.map(row => [row.time, row]));
  const padded30d = weeks.map(time => ({
    time,
    calls: Number(usage30dMap[time]?.calls || 0),
    credits: Number(usage30dMap[time]?.credits || 0)
  }));

  // Format chart data for UsageChart
  const chartData = {
    '24h': padded24h,
    '7d': padded7d,
    '30d': padded30d,
  };
  console.log("!!!CHART DATA", chartData);

  return (
    <div className="min-h-screen bg-gradient-to-br from-white via-blue-50 to-purple-100 dark:from-slate-900 dark:via-purple-900 dark:to-slate-900">
      {/* Background Effects */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute inset-0 bg-gradient-to-r from-purple-500/10 to-blue-500/10 dark:from-purple-500/20 dark:to-blue-500/20"></div>
        <div
          className="absolute inset-0"
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          }}
        ></div>
      </div>

      <div className="relative">
        {/* Dashboard Header */}
        <DashboardHeader />

        {/* Main Dashboard Content */}
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Stats Overview */}
          <StatsOverview
            data={{
              tier: apiData.rows[0].tier,
              credits_used: apiData.rows[0].credits_used_this_month,
              max_credits_per_month: apiData.rows[0].max_credits_per_month,
              usage_data: usageData,
              active_websocket_connections: websocketSessions.rows.length,
              max_websocket_connections: apiData.rows[0].max_websocket_connections
            }}
          />

          {/* Quick Actions Bar */}
          <div className="mt-8">
            <QuickActions />
          </div>

          {/* Main Dashboard Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mt-8">
            {/* Left Column - Main Content */}
            <div className="lg:col-span-2 space-y-8">
              <UsageChart chartData={chartData} />
              <RecentActivity />
            </div>

            {/* Right Column - Sidebar */}
            <div className="space-y-8">
              <ApiKeyManager />
              <WebSocketStatus />
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}

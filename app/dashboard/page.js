import { query } from "@/libs/postgres";
import { query as queryApi } from "@/libs/postgres_api";
import { getServerSession } from "next-auth";
import { authOptions } from "@/libs/next-auth";

import DashboardHeader from "@/components/dashboard/DashboardHeader";
import StatsOverview from "@/components/dashboard/StatsOverview";
import ApiKeyManager from "@/components/dashboard/ApiKeyManager";
import UsageChart from "@/components/dashboard/UsageChart";
import WebSocketStatus from "@/components/dashboard/WebSocketStatus";
import QuickActions from "@/components/dashboard/QuickActions";
import RecentActivity from "@/components/dashboard/RecentActivity";

export const dynamic = "force-dynamic";

// This is a private page: It's protected by the layout.js component which ensures the user is authenticated.
// It's a server compoment which means you can fetch data (like the user profile) before the page is rendered.
// See https://shipfa.st/docs/tutorials/private-page
export default async function Dashboard() {
  const session = await getServerSession(authOptions);
  console.log("!!!SESSION", session);

  // ------------------------------------------------------------ //
  // get the apiKey from the database
  const apiKey = await query('SELECT "apiKey" FROM users WHERE id = $1', [
    session.user.id,
  ]);
  console.log("!!!API KEY", apiKey.rows[0].apiKey);

  // ------------------------------------------------------------ //
  // get the api data
  const apiData = await queryApi(
    "SELECT a.*, b.name, b.max_credits_per_month, b.allowed_endpoints, b.max_websocket_connections FROM users a LEFT JOIN access_tiers b ON a.tier_id = b.id WHERE a.id = $1",
    [apiKey.rows[0].apiKey]
  );
  /* 
  {
    id: 'b5180f95-c6a0-40fd-83d8-e795b8388e40',
    email: '<EMAIL>',
    password_hash: '$2a$10$rOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZQZQZOzJqQZQZQZQZQ',
    api_key: 'test_basic_api_key_12345',
    tier_id: 2,
    credits_remaining: 999932,
    credits_used_this_month: 71,
    total_credits_purchased: 0,
    is_active: true,
    last_login: null,
    created_at: 2025-06-04T05:13:34.252Z,
    updated_at: 2025-06-08T03:22:23.848Z,
    name: 'basic',
    max_credits_per_month: 1000000,
    allowed_endpoints: [
      '/api/v1/status',
      '/api/v1/kol-feed/history',
      '/api/v1/smart-money/daily-trends/most-bought-tokens',
      '/api/v1/kol-feed/history',
      '/api/v1/smart-money/daily-trends/most-sol-tokens',
      '/api/v1/smart-money/daily-trends/daily-flows-sol',
      '/api/v1/smart-money/daily-trends/daily-flows-meme',
      '/api/v1/smart-money/top-tokens/24h',
      '/api/v1/smart-money/top-tokens/3d',
      '/api/v1/smart-money/top-tokens/7d',
      '/api/v1/smart-money/bottom-tokens/24h',
      '/api/v1/smart-money/bottom-tokens/3d',
      '/api/v1/smart-money/bottom-tokens/7d'
    ],
    max_websocket_connections: 3
  }
  */
  console.log("!!!API DATA", apiData.rows);

  // ------------------------------------------------------------ //
  // get usage data
  const apiUsageData = await queryApi(
    `SELECT 
        COUNT(*) as total_requests,
        SUM(credits_consumed) as total_credits_consumed,
        AVG(response_time_ms) as avg_response_time,
        COUNT(DISTINCT endpoint) as unique_endpoints,
        DATE_TRUNC('day', created_at) as date,
        COUNT(*) as daily_requests
     FROM api_usage_logs 
     WHERE user_id = $1 AND created_at >= CURRENT_DATE - INTERVAL '1 day'
     GROUP BY DATE_TRUNC('day', created_at)
     ORDER BY date DESC`,
    [apiKey.rows[0].apiKey]
  );
  /* 
  {
    total_requests: '2',
    total_credits_consumed: '4',
    avg_response_time: null,
    unique_endpoints: '1',
    date: 2025-06-08T00:00:00.000Z,
    daily_requests: '2'
  }
  */
  const usageData =
    apiUsageData.rows.length > 0
      ? apiUsageData.rows[0]
      : {
          total_requests: 0,
          total_credits_consumed: 0,
          avg_response_time: null,
          unique_endpoints: 0,
          date: new Date(),
          daily_requests: 0,
        };
  console.log("!!!API USAGE DATA", apiUsageData.rows);

  // ------------------------------------------------------------ //
  // get websocket sessions websocket_sessions
  const websocketSessions = await queryApi(
    "SELECT * FROM websocket_sessions WHERE user_id = $1 AND disconnected_at IS NULL",
    [apiKey.rows[0].apiKey]
  );
  console.log("!!!WEBSOCKET SESSIONS", websocketSessions.rows);
  /* 
  [
    {
      id: '7d7ad69a-d050-4c6f-a88c-381c24ef5e44',
      user_id: 'b5180f95-c6a0-40fd-83d8-e795b8388e40',
      session_id: '4f4c3ee2-dfe0-4db8-aa6c-186d6633363d',
      connection_id: '944f60b0-3cd5-42ed-99a1-cdaf058a5cbc',
      subscribed_streams: [ 'kol-feed' ],
      ip_address: '::1',
      user_agent: null,
      connected_at: 2025-06-08T03:37:03.452Z,
      last_activity: 2025-06-08T03:37:06.909Z,
      disconnected_at: null
    }
  ]
  */

  // ------------------------------------------------------------ //
  // get usage chart data with proper dynamic time ranges

  // 24h: group by 3-hour intervals for the last 24 hours (rolling window)
  const usage24h = await queryApi(
    `SELECT
      DATE_TRUNC('hour', created_at) as hour_bucket,
      TO_CHAR(DATE_TRUNC('hour', created_at), 'HH24:00') as time,
      COUNT(*) as calls,
      COALESCE(SUM(credits_consumed), 0) as credits
    FROM api_usage_logs
    WHERE user_id = $1 AND created_at >= NOW() - INTERVAL '24 hours'
    GROUP BY hour_bucket, time
    ORDER BY hour_bucket ASC`,
    [apiKey.rows[0].apiKey]
  );
  console.log("!!!RAW 24H DATA", usage24h.rows);

  // 7d: group by day for the last 7 days (rolling window)
  const usage7d = await queryApi(
    `SELECT
      DATE_TRUNC('day', created_at) as day_bucket,
      TO_CHAR(DATE_TRUNC('day', created_at), 'MM/DD') as time,
      COUNT(*) as calls,
      COALESCE(SUM(credits_consumed), 0) as credits
    FROM api_usage_logs
    WHERE user_id = $1 AND created_at >= NOW() - INTERVAL '7 days'
    GROUP BY day_bucket, time
    ORDER BY day_bucket ASC`,
    [apiKey.rows[0].apiKey]
  );

  // 30d: group by week for the last 30 days (rolling window)
  const usage30d = await queryApi(
    `SELECT
      DATE_TRUNC('week', created_at) as week_bucket,
      TO_CHAR(DATE_TRUNC('week', created_at), 'MM/DD') as time,
      COUNT(*) as calls,
      COALESCE(SUM(credits_consumed), 0) as credits
    FROM api_usage_logs
    WHERE user_id = $1 AND created_at >= NOW() - INTERVAL '30 days'
    GROUP BY week_bucket, time
    ORDER BY week_bucket ASC`,
    [apiKey.rows[0].apiKey]
  );

  // Generate dynamic time ranges and pad missing data points
  const now = new Date();
  console.log("!!!CURRENT TIME", now);
  console.log("!!!24H AGO", new Date(now.getTime() - 24 * 60 * 60 * 1000));

  // Generate 7d time slots (last 7 days, daily intervals)
  const generate7dSlots = () => {
    const slots = [];
    for (let i = 6; i >= 0; i--) {
      const slotTime = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
      const timeLabel = slotTime.toLocaleDateString("en-US", {
        month: "2-digit",
        day: "2-digit",
      });
      slots.push({
        bucket: slotTime.toISOString().slice(0, 10) + "T00:00:00.000Z", // Day bucket for matching
        time: timeLabel,
        calls: 0,
        credits: 0,
      });
    }
    return slots;
  };

  // Generate 30d time slots (last 4 weeks, weekly intervals)
  const generate30dSlots = () => {
    const slots = [];
    for (let i = 3; i >= 0; i--) {
      const slotTime = new Date(now.getTime() - i * 7 * 24 * 60 * 60 * 1000);
      // Get start of week (Monday)
      const startOfWeek = new Date(slotTime);
      const day = startOfWeek.getDay();
      const diff = startOfWeek.getDate() - day + (day === 0 ? -6 : 1);
      startOfWeek.setDate(diff);

      const timeLabel = startOfWeek.toLocaleDateString("en-US", {
        month: "2-digit",
        day: "2-digit",
      });
      slots.push({
        bucket: startOfWeek.toISOString().slice(0, 10) + "T00:00:00.000Z", // Week bucket for matching
        time: timeLabel,
        calls: 0,
        credits: 0,
      });
    }
    return slots;
  };

  // Merge database results with generated time slots
  const merge24hData = () => {
    // Simplified approach: just use the raw hourly data and pick every 3rd hour
    const hourlyDataMap = new Map();

    // Create map from raw hourly data
    usage24h.rows.forEach((row) => {
      const hourBucket =
        new Date(row.hour_bucket).toISOString().slice(0, 13) + ":00:00.000Z";
      hourlyDataMap.set(hourBucket, {
        calls: Number(row.calls),
        credits: Number(row.credits),
      });
    });

    console.log("!!!HOURLY DATA MAP", Array.from(hourlyDataMap.entries()));

    // Generate 8 time slots for last 24 hours (every 3 hours)
    const result = [];
    for (let i = 21; i >= 0; i -= 3) {
      const slotTime = new Date(now.getTime() - i * 60 * 60 * 1000);

      // Find the closest hour bucket that has data
      let totalCalls = 0;
      let totalCredits = 0;

      // Aggregate data from this hour and the next 2 hours
      for (let j = 0; j < 3; j++) {
        const checkTime = new Date(slotTime.getTime() + j * 60 * 60 * 1000);
        const checkBucket =
          checkTime.toISOString().slice(0, 13) + ":00:00.000Z";
        const data = hourlyDataMap.get(checkBucket);
        if (data) {
          totalCalls += data.calls;
          totalCredits += data.credits;
        }
      }

      const timeLabel =
        slotTime.toLocaleTimeString("en-US", {
          hour: "2-digit",
          hour12: false,
        }) + ":00";

      result.push({
        time: timeLabel,
        calls: totalCalls,
        credits: totalCredits,
      });
    }

    console.log("!!!FINAL 24H RESULT", result);
    return result;
  };

  const merge7dData = () => {
    const slots = generate7dSlots();
    const dataMap = new Map();

    // Create map from database results using day bucket
    usage7d.rows.forEach((row) => {
      const dayBucket =
        new Date(row.day_bucket).toISOString().slice(0, 10) + "T00:00:00.000Z";
      dataMap.set(dayBucket, {
        calls: Number(row.calls),
        credits: Number(row.credits),
      });
    });

    // Merge with time slots
    return slots.map((slot) => ({
      time: slot.time,
      calls: dataMap.get(slot.bucket)?.calls || 0,
      credits: dataMap.get(slot.bucket)?.credits || 0,
    }));
  };

  const merge30dData = () => {
    const slots = generate30dSlots();
    const dataMap = new Map();

    // Create map from database results using week bucket
    usage30d.rows.forEach((row) => {
      const weekBucket =
        new Date(row.week_bucket).toISOString().slice(0, 10) + "T00:00:00.000Z";
      dataMap.set(weekBucket, {
        calls: Number(row.calls),
        credits: Number(row.credits),
      });
    });

    // Merge with time slots
    return slots.map((slot) => ({
      time: slot.time,
      calls: dataMap.get(slot.bucket)?.calls || 0,
      credits: dataMap.get(slot.bucket)?.credits || 0,
    }));
  };

  // Generate final chart data
  const chartData = {
    "24h": merge24hData(),
    "7d": merge7dData(),
    "30d": merge30dData(),
  };
  console.log("!!!CHART DATA", chartData);

  return (
    <div className="min-h-screen bg-gradient-to-br from-white via-blue-50 to-purple-100 dark:from-slate-900 dark:via-purple-900 dark:to-slate-900">
      {/* Background Effects */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute inset-0 bg-gradient-to-r from-purple-500/10 to-blue-500/10 dark:from-purple-500/20 dark:to-blue-500/20"></div>
        <div
          className="absolute inset-0"
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          }}
        ></div>
      </div>

      <div className="relative">
        {/* Dashboard Header */}
        <DashboardHeader />

        {/* Main Dashboard Content */}
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Stats Overview */}
          <StatsOverview
            data={{
              tier: apiData.rows[0].tier,
              credits_used: apiData.rows[0].credits_used_this_month,
              max_credits_per_month: apiData.rows[0].max_credits_per_month,
              usage_data: usageData,
              active_websocket_connections: websocketSessions.rows.length,
              max_websocket_connections:
                apiData.rows[0].max_websocket_connections,
            }}
          />

          {/* Quick Actions Bar */}
          <div className="mt-8">
            <QuickActions />
          </div>

          {/* Main Dashboard Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mt-8">
            {/* Left Column - Main Content */}
            <div className="lg:col-span-2 space-y-8">
              <UsageChart chartData={chartData} />
              <RecentActivity />
            </div>

            {/* Right Column - Sidebar */}
            <div className="space-y-8">
              <ApiKeyManager
                apiKey={apiData.rows[0].api_key}
                apiKeyCreatedAt={apiData.rows[0].created_at}
                apiKeyLastUsed={apiData.rows[0].last_login}
                apiKeyUsageCount={apiData.rows[0].credits_used_this_month}
              />
              <WebSocketStatus />
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}

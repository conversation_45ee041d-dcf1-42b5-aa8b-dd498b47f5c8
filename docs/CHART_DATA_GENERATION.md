# Chart Data Generation Documentation

## Overview

The dashboard chart data generation system provides dynamic, rolling time windows for API usage visualization. This document explains how the chart data is generated, processed, and displayed.

## Time Range Logic

### 24-Hour Chart
- **Window**: Rolling 24-hour period from current time backwards
- **Intervals**: Hourly buckets
- **Example**: If current time is 3:00 PM Wednesday, shows data from 3:00 PM Tuesday to 3:00 PM Wednesday
- **Time Labels**: HH:MM format (e.g., "15:00", "16:00")

### 7-Day Chart
- **Window**: Rolling 7-day period from current date backwards
- **Intervals**: Daily buckets
- **Example**: If today is Wednesday, shows data from last Thursday to today (Wednesday)
- **Time Labels**: MM/DD format (e.g., "12/15", "12/16")

### 30-Day Chart
- **Window**: Rolling 4-week period from current date backwards
- **Intervals**: Weekly buckets (Monday start)
- **Example**: Shows last 4 weeks of data
- **Time Labels**: MM/DD format for week start dates (e.g., "12/01", "12/08")

## Database Queries

### 24-Hour Query
```sql
SELECT 
  DATE_TRUNC('hour', created_at) as hour_bucket,
  TO_CHAR(DATE_TRUNC('hour', created_at), 'HH24:00') as time,
  COUNT(*) as calls,
  COALESCE(SUM(credits_consumed), 0) as credits
FROM api_usage_logs
WHERE user_id = $1 AND created_at >= NOW() - INTERVAL '24 hours'
GROUP BY hour_bucket, time
ORDER BY hour_bucket ASC
```

### 7-Day Query
```sql
SELECT 
  DATE_TRUNC('day', created_at) as day_bucket,
  TO_CHAR(DATE_TRUNC('day', created_at), 'MM/DD') as time,
  COUNT(*) as calls,
  COALESCE(SUM(credits_consumed), 0) as credits
FROM api_usage_logs
WHERE user_id = $1 AND created_at >= NOW() - INTERVAL '7 days'
GROUP BY day_bucket, time
ORDER BY day_bucket ASC
```

### 30-Day Query
```sql
SELECT 
  DATE_TRUNC('week', created_at) as week_bucket,
  TO_CHAR(DATE_TRUNC('week', created_at), 'MM/DD') as time,
  COUNT(*) as calls,
  COALESCE(SUM(credits_consumed), 0) as credits
FROM api_usage_logs
WHERE user_id = $1 AND created_at >= NOW() - INTERVAL '30 days'
GROUP BY week_bucket, time
ORDER BY week_bucket ASC
```

## Data Processing Pipeline

### 1. Time Slot Generation
Each chart type generates a complete set of time slots to ensure consistent visualization:

```javascript
// 24h: Generate 24 hourly slots
for (let i = 23; i >= 0; i--) {
  const slotTime = new Date(now.getTime() - (i * 60 * 60 * 1000));
  // Create time slot with bucket for matching
}

// 7d: Generate 7 daily slots
for (let i = 6; i >= 0; i--) {
  const slotTime = new Date(now.getTime() - (i * 24 * 60 * 60 * 1000));
  // Create time slot with bucket for matching
}

// 30d: Generate 4 weekly slots
for (let i = 3; i >= 0; i--) {
  const slotTime = new Date(now.getTime() - (i * 7 * 24 * 60 * 60 * 1000));
  // Create time slot with bucket for matching
}
```

### 2. Data Merging
Database results are merged with generated time slots to fill gaps:

```javascript
const merge24hData = () => {
  const slots = generate24hSlots();
  const dataMap = new Map();
  
  // Map database results by time bucket
  usage24h.rows.forEach(row => {
    const hourBucket = new Date(row.hour_bucket).toISOString().slice(0, 13) + ':00:00.000Z';
    dataMap.set(hourBucket, {
      calls: Number(row.calls),
      credits: Number(row.credits)
    });
  });
  
  // Merge with time slots, filling gaps with zeros
  return slots.map(slot => ({
    time: slot.time,
    calls: dataMap.get(slot.bucket)?.calls || 0,
    credits: dataMap.get(slot.bucket)?.credits || 0
  }));
};
```

### 3. Final Data Structure
The final chart data structure:

```javascript
const chartData = {
  '24h': [
    { time: "14:00", calls: 5, credits: 10 },
    { time: "15:00", calls: 8, credits: 16 },
    // ... 24 entries total
  ],
  '7d': [
    { time: "12/15", calls: 45, credits: 90 },
    { time: "12/16", calls: 52, credits: 104 },
    // ... 7 entries total
  ],
  '30d': [
    { time: "12/01", calls: 320, credits: 640 },
    { time: "12/08", calls: 285, credits: 570 },
    // ... 4 entries total
  ]
};
```

## Chart Visualization

### Data Flow
1. **Database Query**: Fetch usage data with proper time bucketing
2. **Time Slot Generation**: Create complete time range with all intervals
3. **Data Merging**: Combine database results with time slots
4. **Chart Rendering**: Display bars with proper scaling and tooltips

### Visual Features
- **Bar Heights**: Calculated based on maximum value in dataset
- **Minimum Heights**: Ensure visibility even for small values
- **Tooltips**: Show exact values on hover
- **Time Labels**: Display actual dates/times below bars
- **Responsive Design**: Adapts to different screen sizes

## Troubleshooting

### Common Issues
1. **No Data Showing**: Check database connection and user_id parameter
2. **Incorrect Time Ranges**: Verify server timezone settings
3. **Missing Data Points**: Ensure time slot generation covers full range
4. **Performance Issues**: Consider adding database indexes on created_at and user_id

### Debug Information
The system logs comprehensive debug information:
```javascript
console.log("!!!CHART DATA", chartData);
```

Check browser console for:
- Raw database query results
- Generated time slots
- Final merged data structure
- Any error messages

## Performance Considerations

### Database Optimization
- Indexes on `(user_id, created_at)` for efficient querying
- DATE_TRUNC functions for proper time bucketing
- COALESCE for handling null values

### Frontend Optimization
- Efficient data merging using Map objects
- Minimal DOM updates in chart component
- Responsive design for mobile devices

## Future Enhancements

### Potential Improvements
1. **Custom Time Ranges**: Allow users to select specific date ranges
2. **Real-time Updates**: WebSocket integration for live data
3. **Data Export**: CSV/JSON export functionality
4. **Advanced Filtering**: Filter by endpoint, status code, etc.
5. **Caching**: Redis caching for frequently accessed data

### Scalability Considerations
- Pagination for large datasets
- Data aggregation for long time periods
- Background processing for heavy calculations

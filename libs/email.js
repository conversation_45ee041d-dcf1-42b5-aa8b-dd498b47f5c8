import { createTransport } from 'nodemailer';
import config from '@/config';

// SECURITY: This file should ONLY be imported in server-side code (API routes, middleware, etc.)
// NEVER import this in client-side components or pages

// Ensure this is only used server-side
if (typeof window !== 'undefined') {
  throw new Error('❌ SECURITY ERROR: Email client cannot be used on the client side!')
}

// Create reusable transporter object using SMTP transport
let transporter = null;

function createTransporter() {
  if (!transporter) {
    const transportConfig = {
      host: process.env.SMTP_HOST,
      port: parseInt(process.env.SMTP_PORT) || 587,
      secure: process.env.SMTP_SECURE === 'true', // true for 465, false for other ports
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASSWORD,
      },
      // Additional security options
      tls: {
        rejectUnauthorized: process.env.SMTP_REJECT_UNAUTHORIZED !== 'false'
      }
    };

    transporter = createTransport(transportConfig);
    
    console.log('✅ SMTP transporter created');
  }
  
  return transporter;
}

// Verify SMTP connection
export async function verifyEmailConnection() {
  try {
    const transport = createTransporter();
    await transport.verify();
    console.log('✅ SMTP server connection verified');
    return { success: true, message: 'SMTP connection verified' };
  } catch (error) {
    console.error('❌ SMTP connection failed:', error.message);
    return { success: false, message: 'SMTP connection failed', error: error.message };
  }
}

// Send email function
export async function sendEmail({ to, subject, text, html, from = null }) {
  try {
    if (!process.env.SMTP_HOST || !process.env.SMTP_USER || !process.env.SMTP_PASSWORD) {
      throw new Error('SMTP configuration is incomplete. Please check environment variables.');
    }

    const transport = createTransporter();
    
    const mailOptions = {
      from: from || process.env.SMTP_FROM || config.resend.fromNoReply,
      to: Array.isArray(to) ? to.join(', ') : to,
      subject,
      text,
      html,
    };

    const result = await transport.sendMail(mailOptions);
    
    console.log('✅ Email sent successfully:', { messageId: result.messageId });

    return {
      success: true,
      messageId: result.messageId,
      response: result.response
    };
  } catch (error) {
    console.error('❌ Email sending failed:', error.message);
    return {
      success: false,
      error: error.message
    };
  }
}

// Send magic link email (for NextAuth)
export async function sendMagicLinkEmail({ email, url, appName = config.appName }) {
  const subject = `Sign in to ${appName}`;
  const text = `Sign in to ${appName}\n\nClick the link below to sign in:\n${url}\n\nThis link will expire in 24 hours.\n\nIf you did not request this email, you can safely ignore it.`;
  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
      <div style="text-align: center; margin-bottom: 30px;">
        <h1 style="color: #333; margin: 0;">${appName}</h1>
      </div>
      
      <h2 style="color: #333; margin-bottom: 20px;">Sign in to your account</h2>
      
      <p style="color: #666; font-size: 16px; line-height: 1.5; margin-bottom: 30px;">
        Click the button below to securely sign in to your account. This magic link will expire in 24 hours.
      </p>
      
      <div style="text-align: center; margin: 40px 0;">
        <a href="${url}" 
           style="background-color: #007bff; color: white; padding: 14px 28px; text-decoration: none; border-radius: 6px; display: inline-block; font-weight: 600; font-size: 16px;">
          Sign In Securely
        </a>
      </div>
      
      <div style="background-color: #f8f9fa; padding: 20px; border-radius: 6px; margin: 30px 0;">
        <p style="color: #666; font-size: 14px; margin: 0; line-height: 1.4;">
          <strong>Can't click the button?</strong><br>
          Copy and paste this link in your browser:<br>
          <a href="${url}" style="color: #007bff; word-break: break-all;">${url}</a>
        </p>
      </div>
      
      <div style="border-top: 1px solid #eee; padding-top: 20px; margin-top: 40px;">
        <p style="color: #999; font-size: 12px; line-height: 1.4; margin: 0;">
          This email was sent to ${email}. If you did not request this sign-in link, you can safely ignore this email.
          For security reasons, this link will expire in 24 hours.
        </p>
      </div>
    </div>
  `;

  return await sendEmail({ to: email, subject, text, html });
}

// Send welcome email
export async function sendWelcomeEmail({ email, name, appName = config.appName }) {
  const subject = `Welcome to ${appName}!`;
  const text = `Welcome to ${appName}!\n\nHi ${name || 'there'},\n\nThank you for joining ${appName}. We're excited to have you on board!\n\nIf you have any questions, feel free to reach out to our support team.\n\nBest regards,\nThe ${appName} Team`;
  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
      <div style="text-align: center; margin-bottom: 30px;">
        <h1 style="color: #333; margin: 0;">${appName}</h1>
      </div>
      
      <h2 style="color: #333; margin-bottom: 20px;">Welcome to ${appName}!</h2>
      
      <p style="color: #666; font-size: 16px; line-height: 1.5;">
        Hi ${name || 'there'},
      </p>
      
      <p style="color: #666; font-size: 16px; line-height: 1.5;">
        Thank you for joining ${appName}. We're excited to have you on board!
      </p>
      
      <p style="color: #666; font-size: 16px; line-height: 1.5;">
        If you have any questions, feel free to reach out to our support team.
      </p>
      
      <div style="border-top: 1px solid #eee; padding-top: 20px; margin-top: 40px;">
        <p style="color: #666; font-size: 14px;">
          Best regards,<br>
          The ${appName} Team
        </p>
      </div>
    </div>
  `;

  return await sendEmail({ to: email, subject, text, html });
}

// Enforce strong SMTP_PASSWORD in production
if (process.env.NODE_ENV === 'production') {
  const pwd = process.env.SMTP_PASSWORD;
  if (!pwd || typeof pwd !== 'string' || pwd.length < 16 || /smtp|changeme|default|1234|password/i.test(pwd)) {
    throw new Error('❌ SECURITY ERROR: SMTP_PASSWORD must be set, at least 16 characters, and not a weak/default value in production.');
  }
}

export default { sendEmail, sendMagicLinkEmail, sendWelcomeEmail, verifyEmailConnection };

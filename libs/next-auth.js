import GoogleProvider from "next-auth/providers/google";
import EmailProvider from "next-auth/providers/email";
import { PrismaAdapter } from "@auth/prisma-adapter";
import config from "@/config";
import { prisma } from "./prisma";

// Enforce strong NEXTAUTH_SECRET at startup
if (!process.env.NEXTAUTH_SECRET || typeof process.env.NEXTAUTH_SECRET !== 'string' || process.env.NEXTAUTH_SECRET.length < 32 || /secret|changeme|default|1234|password/i.test(process.env.NEXTAUTH_SECRET)) {
  throw new Error('❌ SECURITY ERROR: NEXTAUTH_SECRET must be set, at least 32 characters, and not a weak/default value. Please set a strong, random secret in your environment variables.');
}

export const authOptions = {
  // Set any random key in .env.local
  secret: process.env.NEXTAUTH_SECRET,
  providers: [
    // Google OAuth provider - conditionally enabled
    ...(config.enabledAuth.google && process.env.GOOGLE_ID && process.env.GOOGLE_SECRET
      ? [
          GoogleProvider({
            // Follow the "Login with Google" tutorial to get your credentials
            clientId: process.env.GOOGLE_ID,
            clientSecret: process.env.GOOGLE_SECRET,
            async profile(profile) {
              return {
                id: profile.sub,
                name: profile.given_name ? profile.given_name : profile.name,
                email: profile.email,
                image: profile.picture,
                createdAt: new Date(),
              };
            },
          }),
        ]
      : []),
    // Email provider with magic links using your SMTP server
    // Requires a PostgreSQL database adapter to store verification tokens
    ...(config.enabledAuth.email && process.env.POSTGRE_DB && process.env.SMTP_HOST
      ? [
          EmailProvider({
            server: {
              host: process.env.SMTP_HOST,
              port: parseInt(process.env.SMTP_PORT) || 587,
              secure: process.env.SMTP_SECURE === 'true', // true for 465, false for other ports
              auth: {
                user: process.env.SMTP_USER,
                pass: process.env.SMTP_PASSWORD,
              },
              // Additional security options
              tls: {
                rejectUnauthorized: process.env.SMTP_REJECT_UNAUTHORIZED !== 'false'
              }
            },
            from: process.env.SMTP_FROM || config.resend.fromNoReply,
            // Magic link configuration
            maxAge: 24 * 60 * 60, // 24 hours
            // Custom email template (optional)
            sendVerificationRequest: async ({ identifier: email, url, provider }) => {
              const { createTransport } = await import('nodemailer');

              const transport = createTransport(provider.server);

              const result = await transport.sendMail({
                to: email,
                from: provider.from,
                subject: `Sign in to ${config.appName}`,
                text: `Sign in to ${config.appName}\n\nClick the link below to sign in:\n${url}\n\nThis link will expire in 24 hours.\n\nIf you did not request this email, you can safely ignore it.`,
                html: `
                  <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                    <h2 style="color: #333;">Sign in to ${config.appName}</h2>
                    <p>Click the button below to sign in to your account:</p>
                    <div style="text-align: center; margin: 30px 0;">
                      <a href="${url}" style="background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">Sign In</a>
                    </div>
                    <p style="color: #666; font-size: 14px;">
                      Or copy and paste this link in your browser:<br>
                      <a href="${url}" style="color: #007bff;">${url}</a>
                    </p>
                    <p style="color: #666; font-size: 12px;">
                      This link will expire in 24 hours. If you did not request this email, you can safely ignore it.
                    </p>
                  </div>
                `,
              });

              console.log('✅ Magic link email sent:', { messageId: result.messageId });
            },
          }),
        ]
      : []),
  ],
  // Database adapter for storing user data in PostgreSQL
  // Using Prisma adapter with error handling
  ...(process.env.POSTGRE_DB && { adapter: PrismaAdapter(prisma) }),

  callbacks: {
    session: async ({ session, token }) => {
      if (session?.user) {
        session.user.id = token.sub;
      }
      return session;
    },
  },
  session: {
    strategy: "jwt",
    maxAge: 60 * 60, // 1 hour session expiry for security
  },
  theme: {
    brandColor: config.colors.main,
    // Add you own logo below. Recommended size is rectangle (i.e. 200x50px) and show your logo + name.
    // It will be used in the login flow to display your logo. If you don't add it, it will look faded.
    logo: `https://${config.domainName}/logoAndName.png`,
  },
};
